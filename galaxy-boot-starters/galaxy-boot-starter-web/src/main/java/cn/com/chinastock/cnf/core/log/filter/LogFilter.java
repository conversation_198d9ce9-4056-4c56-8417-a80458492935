package cn.com.chinastock.cnf.core.log.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.logger.FrameworkLogger;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Log Context 过滤器
 *
 * <p>过滤器，主要功能如下：</p>
 * <ul>
 *     <li>处理请求中的信息，提取 HTTP method、HTTP request URI、 traceId、spanId 和 parentSpanId</li>
 *     <li>记录请求日志（可通过配置控制）</li>
 *     <li>记录响应日志（可通过配置控制）</li>
 *     <li>记录性能日志（可通过配置控制）</li>
 * </ul>
 * 
 * <p>特殊处理：</p>
 * <ul>
 *     <li>对于SSE（Server-Sent Events）响应，跳过响应体缓存以避免阻塞流式传输</li>
 *     <li>对于MCP SSE端点，确保实时事件流能够正常传输</li>
 * </ul>
 */
public class LogFilter extends OncePerRequestFilter implements Ordered {
    private final ITraceContext traceContext;
    private final LogProperties logProperties;
    private final FrameworkLogger frameworkLogger;

    public LogFilter(ITraceContext traceContext, LogProperties logProperties) {
        this.traceContext = traceContext;
        this.logProperties = logProperties;
        this.frameworkLogger = new FrameworkLogger(logProperties);
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
        loadLogContext(request);
        CacheableRequestWrapper requestWrapper = new CacheableRequestWrapper(request);
        
        // 检查是否为SSE响应，如果是则跳过响应体缓存
        boolean isSSEResponse = isSSEResponse(request, response);
        
        if (isSSEResponse) {
            // 对于SSE响应，直接使用原始响应，不进行缓存
            writeRequestLogAndStartWatch(requestWrapper);
            try {
                filterChain.doFilter(requestWrapper, response);
            } finally {
                writeSSEResponseLogAndStopWatch(response);
            }
        } else {
            // 对于普通响应，使用ContentCachingResponseWrapper进行缓存
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
            writeRequestLogAndStartWatch(requestWrapper);
            try {
                filterChain.doFilter(requestWrapper, responseWrapper);
            } finally {
                writeResponseLogAndStopWatch(responseWrapper);
            }
        }
    }

    /**
     * 判断是否为SSE响应
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 如果是SSE响应返回true，否则返回false
     */
    private boolean isSSEResponse(HttpServletRequest request, HttpServletResponse response) {
        // 如果SSE响应处理被禁用，直接返回false
        if (!logProperties.isSseResponseHandlingEnabled()) {
            return false;
        }
        
        // 检查请求路径是否为SSE端点
        String requestURI = request.getRequestURI();
        if (requestURI != null) {
            List<String> ssePatterns = logProperties.getSseEndpointPatterns();
            for (String pattern : ssePatterns) {
                if (requestURI.contains(pattern)) {
                    return true;
                }
            }
        }
        
        // 检查Accept头是否包含text/event-stream
        String acceptHeader = request.getHeader("Accept");
        if (acceptHeader != null && acceptHeader.contains("text/event-stream")) {
            return true;
        }
        
        // 检查Content-Type是否为text/event-stream（响应头）
        String contentType = response.getContentType();
        if (contentType != null && contentType.contains("text/event-stream")) {
            return true;
        }
        
        return false;
    }

    private void loadLogContext(HttpServletRequest request) {
        LogContext.current().loadContext(logProperties, traceContext, request.getMethod(), request.getRequestURI(), getRequestHeaders(request));
    }

    private void writeRequestLogAndStartWatch(CacheableRequestWrapper requestWrapper) {
        try {
            frameworkLogger.startPerformanceWatch();
            frameworkLogger.logRequest(requestWrapper);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeRequestLogAndStartWatch: exception occurred", e);
        }
    }

    private void writeResponseLogAndStopWatch(ContentCachingResponseWrapper responseWrapper) {
        try {
            frameworkLogger.logResponse(responseWrapper);
            responseWrapper.copyBodyToResponse();

            frameworkLogger.stopPerformanceWatch();
            LogContext.clear();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeResponseLogAndStopWatch: exception occurred", e);
        }
    }

    /**
     * 处理SSE响应的日志记录
     * 
     * @param response 原始HTTP响应
     */
    private void writeSSEResponseLogAndStopWatch(HttpServletResponse response) {
        try {
            // 对于SSE响应，只记录基本的响应信息，不记录响应体
            frameworkLogger.logSSEResponse(response);
            
            frameworkLogger.stopPerformanceWatch();
            LogContext.clear();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeSSEResponseLogAndStopWatch: exception occurred", e);
        }
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest httpRequest) {
        Map<String, String> headers = new HashMap<>();
        httpRequest.getHeaderNames()
                .asIterator()
                .forEachRemaining(headerName -> headers.put(headerName, httpRequest.getHeader(headerName)));
        return headers;
    }

    @Override
    public int getOrder() {
        // 确保在 OpenTelemetry tracing filter 之后执行
        return Ordered.HIGHEST_PRECEDENCE + 2000;
    }
}