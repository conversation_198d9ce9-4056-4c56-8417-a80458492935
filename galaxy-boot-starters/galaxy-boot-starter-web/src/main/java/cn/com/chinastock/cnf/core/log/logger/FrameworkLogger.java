package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * 框架日志记录器
 *
 * <p>用于记录请求日志、响应日志和性能日志</p>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #logRequest(HttpServletRequest)}：记录请求日志。</li>
 *     <li>{@link #logResponse(HttpServletResponse)}：记录响应日志。</li>
 *     <li>{@link #startPerformanceWatch()}：启动性能监控。</li>
 *     <li>{@link #stopPerformanceWatch()}：停止性能监控并记录耗时。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class FrameworkLogger {
    private final LogProperties logProperties;
    private final RequestLogger requestLogger;
    private final ResponseLogger responseLogger;
    private final PerformanceLogger performanceLogger;

    /**
     * 构造函数
     *
     * @param logProperties 日志配置属性
     */
    public FrameworkLogger(LogProperties logProperties) {
        this.logProperties = logProperties;
        this.requestLogger = new RequestLogger(logProperties);
        this.responseLogger = new ResponseLogger(logProperties);
        this.performanceLogger = new PerformanceLogger();
    }

    /**
     * 记录HTTP请求日志
     *
     * @param request HTTP请求对象
     */
    public void logRequest(HttpServletRequest request) {
        if (logProperties.isReqResLogEnabledButNotControllerLog()) {
            requestLogger.log(request);
        }
    }

    /**
     * 记录HTTP响应日志
     *
     * @param response HTTP响应对象
     */
    public void logResponse(HttpServletResponse response) {
        if (logProperties.isReqResLogEnabledButNotControllerLog()) {
            responseLogger.log(response);
        }
    }

    /**
     * 记录SSE响应日志
     * 
     * <p>对于SSE（Server-Sent Events）响应，只记录基本的响应信息，不记录响应体内容，
     * 以避免阻塞流式传输。</p>
     *
     * @param response HTTP响应对象
     */
    public void logSSEResponse(HttpServletResponse response) {
        if (logProperties.isReqResLogEnabledButNotControllerLog()) {
            responseLogger.logSSEResponse(response);
        }
    }

    /**
     * 启动性能监控
     */
    public void startPerformanceWatch() {
        if (logProperties.isPerformanceLogEnabled()) {
            performanceLogger.start();
        }
    }

    /**
     * 停止性能监控并记录耗时
     */
    public void stopPerformanceWatch() {
        if (logProperties.isPerformanceLogEnabled()) {
            performanceLogger.stop();
        }
    }

    /**
     * 记录请求日志，并对带注解字段进行掩码处理。
     *
     * @param requestObject 需要记录的请求对象
     * @throws Exception 如果日志记录过程中发生异常，将记录错误日志
     */
    public void logRequestWithFieldsMasked(Object requestObject) {
        try {
            if (logProperties.isControllerLogEnabled()) {
                requestLogger.logWithFieldsMasked(requestObject);
            }
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log request", e);
        }
    }

    /**
     * 记录响应日志，并对敏感字段进行脱敏处理。
     *
     * @param contentType    响应内容类型
     * @param headers        响应头信息
     * @param statusCode     HTTP状态码
     * @param responseObject 响应对象
     * @throws Exception 如果记录日志时发生异常，会捕获并记录错误日志
     */
    public void logResponseWithFieldsMasked(MediaType contentType, HttpHeaders headers, int statusCode, Object responseObject) {
        try {
            if (logProperties.isControllerLogEnabled()) {
                responseLogger.logWithFieldsMasked(contentType, headers, statusCode, responseObject);
            }
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log response", e);
        }
    }
}
